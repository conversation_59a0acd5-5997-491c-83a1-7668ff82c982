我正在设法搭建公司的的大模型代理（使用LiteLLM），然后让gemini-cli通过代理访问VertexAI上的gemini-2.5-pro。
目前情况我简单回顾下：
1. 我开通的是VertexAI上的大模型，并且拿到了project id和location。
2. LiteLLM上目前采用了`pass-through endpoint`的方式（参考自`https://docs.litellm.ai/docs/pass_through/vertex_ai`）已经配置好了，且curl已经测试OK，测试地址为`http://10.243.65.78:4000/vertex_ai/v1/projects/program-center/locations/us-central1/publishers/google/models/gemini-2.5-pro:generateContent`。
3. 我创建了LiteLLM的虚拟密钥。
4. 我采用`GOOGLE_VERTEX_BASE_URL`（`http://10.243.65.78:4000/vertex_ai`）、`GOOGLE_GENAI_USE_VERTEXAI`（"true"）、`GOOGLE_API_KEY`的方式测试gemini-cli，情况如下：
   - 当`GOOGLE_API_KEY`填写LiteLLM的虚拟密钥时，报错如下：
   ```
   [API Error: {"error":{"message":"Authentication Error, Malformed API Key passed in. Ensure Key has `Bearer ` prefix. Passed in: ","type":"auth_error","param":"None","code":"401"}}]
   ```
   - 当`GOOGLE_API_KEY`填写"Bearer <虚拟密钥>“时，报错如下：
   ```
   [API Error: {"error":{"message":"Authentication Error, LiteLLM Virtual Key expected. Received=Bearer sk-uqiKf8cRu5fW_bvVG_fdPA, expected to start with 'sk-'.","type":"auth_error","param":"None","code":"401"}}]
   ```
5. 我于是下载了当前项目的源码，通过项目依赖我找到了`node_modules/@google/genai`这个库。我简单看了下源码，再结合LiteLLM的源码，我发现gemini-cli发送的是`x-goog-api-key`，而LiteLLM期待的虚拟密钥需要放在`x-litellm-api-key`中。
6. 于是我写了个`litellm_proxy`：将http请求的header中的`x-goog-api-key`复制到`x-litellm-api-key`中，然后请求LiteLLM代理。
7. 然后我重新修改环境变量`GOOGLE_VERTEX_BASE_URL="http://localhost:8081/vertex_ai"`（指向了我的新代理`litellm_proxy`），但新的问题发生了：LiteLLM给我返回了404错误！
8. 我在`litellm_proxy`中打印了请求的URL，发现是`POST /vertex_ai/v1beta1/publishers/google/models/gemini-2.5-pro`，我感觉这里出现了问题，应为根据gemini-cli的源码，请求的地址route应该是`POST /v1/projects/{project}/locations/{location}/publishers/{publisher}/models/{model}:generateContent`。
